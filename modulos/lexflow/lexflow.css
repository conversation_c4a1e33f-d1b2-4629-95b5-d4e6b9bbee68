/* LexFlow Module Styles */

.lexflow-module {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
    min-height: 100vh;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.lexflow-module .module-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    backdrop-filter: blur(10px);
}

.lexflow-module .module-header h1 {
    font-size: 2.5rem;
    margin: 0 0 10px 0;
    font-weight: 300;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.lexflow-module .module-header p {
    font-size: 1.1rem;
    margin: 0;
    opacity: 0.9;
}

.lexflow-module .module-content {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.lexflow-module .dashboard-section h2 {
    font-size: 1.8rem;
    margin-bottom: 20px;
    color: #fff;
    text-align: center;
}

.lexflow-module .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.lexflow-module .stat-card {
    background: rgba(255, 255, 255, 0.15);
    padding: 25px;
    border-radius: 12px;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.lexflow-module .stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.lexflow-module .stat-card h3 {
    font-size: 1rem;
    margin: 0 0 15px 0;
    opacity: 0.9;
    font-weight: 500;
}

.lexflow-module .stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: #fff;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.lexflow-module .actions-section {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.lexflow-module .btn {
    padding: 12px 30px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    min-width: 150px;
}

.lexflow-module .btn-primary {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.lexflow-module .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
}

.lexflow-module .btn-secondary {
    background: linear-gradient(45deg, #74b9ff, #0984e3);
    color: white;
    box-shadow: 0 4px 15px rgba(116, 185, 255, 0.4);
}

.lexflow-module .btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(116, 185, 255, 0.6);
}

/* Responsive design */
@media (max-width: 768px) {
    .lexflow-module {
        padding: 15px;
    }
    
    .lexflow-module .module-header h1 {
        font-size: 2rem;
    }
    
    .lexflow-module .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .lexflow-module .actions-section {
        flex-direction: column;
        align-items: center;
    }
}
