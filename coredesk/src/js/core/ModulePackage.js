/**
 * ModulePackage
 * Abstraction for a dynamic module package in CoreDesk Framework v2.0.0
 * 
 * Features:
 * - Module manifest validation and management
 * - Dynamic module class creation from code string
 * - Installation and uninstallation lifecycle
 * - Asset management (CSS, icons, templates)
 * - Dependency resolution
 * - Version and compatibility checking
 */

class ModulePackage {
    constructor(packageData, options = {}) {
        // Core package data
        this.manifest = packageData.manifest || {};
        this.moduleCode = packageData.moduleCode || '';
        this.styles = packageData.styles || '';
        this.assets = packageData.assets || {};
        
        // Package metadata
        this.id = this.manifest.id;
        this.name = this.manifest.name || this.id;
        this.version = this.manifest.version || '1.0.0';
        this.description = this.manifest.description || '';
        
        // Installation state
        this.isInstalled = false;
        this.isLoaded = false;
        this.isInitialized = false;
        this.installPath = null;
        this.installedAt = null;
        this.initializedAt = null;
        
        // Module class and instance management
        this.moduleClass = null;
        this.moduleInstance = null;
        
        // Configuration
        this.options = {
            validateManifest: true,
            strictMode: true,
            sandboxed: false,
            allowUnsafeCode: false,
            ...options
        };
        
        // Dependencies
        this.dependencies = this.manifest.dependencies || {};
        this.requiredPermissions = this.manifest.permissions || [];
        this.requiredLicense = this.manifest.requiredLicense;
        
        // Logger
        this.logger = window.GlobalLogger || {
            info: (tag, message) => console.log(`[${tag}] ${message}`),
            error: (tag, message, error) => console.error(`[${tag}] ${message}`, error),
            warn: (tag, message) => console.warn(`[${tag}] ${message}`),
            debug: (tag, message) => console.debug(`[${tag}] ${message}`)
        };
        
        // Validate manifest on construction
        if (this.options.validateManifest) {
            this.validateManifest();
        }
    }

    /**
     * Install the module package
     * @returns {Promise<void>}
     */
    async install() {
        try {
            this.logger.info('ModulePackage', `Installing package: ${this.id}`);

            if (this.isInstalled) {
                throw new Error(`Package ${this.id} is already installed`);
            }

            // 1. Validate manifest and compatibility
            this.validateManifest();
            await this.validateCompatibility();

            // 2. Check and validate permissions
            await this.validatePermissions();

            // 3. Create module class from code
            await this.createModuleClass();

            // 4. Install assets (CSS, icons, etc.)
            await this.installAssets();

            // 5. Mark as installed BEFORE saving metadata
            this.isInstalled = true;
            this.installedAt = new Date().toISOString();
            this.logger.info('ModulePackage', `Marked as installed: ${this.id}, timestamp: ${this.installedAt}`);

            // 6. Update local metadata (now with correct isInstalled state)
            this.logger.info('ModulePackage', `About to call updateLocalMetadata() for: ${this.id}`);
            await this.updateLocalMetadata();
            this.logger.info('ModulePackage', `updateLocalMetadata() completed for: ${this.id}`);

            // 7. Verify the installation was persisted correctly
            const storageKey = `coredesk_module_${this.id}`;
            const savedData = localStorage.getItem(storageKey);
            if (savedData) {
                const parsed = JSON.parse(savedData);
                if (!parsed.isInstalled) {
                    this.logger.warn('ModulePackage', `Installation verification failed for ${this.id} - isInstalled is false in saved data`);
                    // Attempt to fix by updating metadata again
                    await this.updateLocalMetadata();
                } else {
                    this.logger.info('ModulePackage', `Installation verified for ${this.id} - isInstalled is true in saved data`);
                }
            }

            this.logger.info('ModulePackage', `Package ${this.id} installed successfully`);

        } catch (error) {
            this.logger.error('ModulePackage', `Failed to install package ${this.id}:`, error);
            
            // Cleanup on failure
            await this.cleanupFailedInstallation();
            throw error;
        }
    }

    /**
     * Initialize the module package
     * This method is called by DynamicModuleManager after validation and style loading
     * @returns {Promise<void>}
     */
    async initialize() {
        try {
            this.logger.info('ModulePackage', `Initializing package: ${this.id}`);

            // 1. Create module class if not already created
            if (!this.moduleClass) {
                await this.createModuleClass();
            }

            // 2. Mark as initialized
            this.isInitialized = true;
            this.initializedAt = new Date().toISOString();

            this.logger.info('ModulePackage', `Package ${this.id} initialized successfully`);

        } catch (error) {
            this.logger.error('ModulePackage', `Failed to initialize package ${this.id}:`, error);
            throw error;
        }
    }

    /**
     * Uninstall the module package
     * @returns {Promise<void>}
     */
    async uninstall() {
        try {
            this.logger.info('ModulePackage', `Uninstalling package: ${this.id}`);

            if (!this.isInstalled) {
                throw new Error(`Package ${this.id} is not installed`);
            }

            // 1. Cleanup module instance if loaded
            if (this.isLoaded && this.moduleInstance) {
                await this.cleanupModuleInstance();
            }

            // 2. Remove assets
            await this.removeAssets();

            // 3. Clear local metadata
            await this.clearLocalMetadata();

            // 4. Reset state
            this.isInstalled = false;
            this.isLoaded = false;
            this.moduleClass = null;
            this.moduleInstance = null;
            this.installPath = null;
            this.installedAt = null;

            this.logger.info('ModulePackage', `Package ${this.id} uninstalled successfully`);

        } catch (error) {
            this.logger.error('ModulePackage', `Failed to uninstall package ${this.id}:`, error);
            throw error;
        }
    }

    /**
     * Get the module class (create if not exists)
     * @returns {Function} Module class constructor
     */
    getModuleClass() {
        if (!this.moduleClass) {
            this.createModuleClass();
        }
        return this.moduleClass;
    }

    /**
     * Create an instance of the module
     * @returns {Object} Module instance
     */
    createModuleInstance() {
        const ModuleClass = this.getModuleClass();
        this.moduleInstance = new ModuleClass();
        this.isLoaded = true;
        return this.moduleInstance;
    }

    /**
     * Get module metadata
     * @returns {Object} Module metadata
     */
    getMetadata() {
        return {
            id: this.id,
            name: this.name,
            version: this.version,
            description: this.description,
            author: this.manifest.author,
            license: this.manifest.license,
            category: this.manifest.category,
            tags: this.manifest.tags || [],
            size: this.getPackageSize(),
            isInstalled: this.isInstalled,
            isLoaded: this.isLoaded,
            installedAt: this.installedAt,
            dependencies: this.dependencies,
            permissions: this.requiredPermissions,
            requiredLicense: this.requiredLicense
        };
    }

    /**
     * Get package styles
     * @returns {string} CSS styles
     */
    getStyles() {
        return this.styles;
    }

    /**
     * Get package assets
     * @returns {Object} Assets object
     */
    getAssets() {
        return this.assets;
    }

    /**
     * Check if package is compatible with current environment
     * @returns {boolean} Compatibility status
     */
    async isCompatible() {
        try {
            await this.validateCompatibility();
            return true;
        } catch {
            return false;
        }
    }

    // Private Methods

    /**
     * Validate the package manifest
     * @private
     */
    validateManifest() {
        const requiredFields = ['id', 'name', 'version', 'main'];
        const manifest = this.manifest;

        // Check required fields
        for (const field of requiredFields) {
            if (!manifest[field]) {
                throw new Error(`Package manifest missing required field: ${field}`);
            }
        }

        // Validate ID format
        if (!/^[a-z][a-z0-9-_]*$/.test(manifest.id)) {
            throw new Error('Package ID must start with a letter and contain only lowercase letters, numbers, hyphens, and underscores');
        }

        // Validate version format (simple semver check)
        if (!/^\d+\.\d+\.\d+/.test(manifest.version)) {
            throw new Error('Package version must follow semantic versioning (x.y.z)');
        }

        // Validate permissions if specified
        if (manifest.permissions && !Array.isArray(manifest.permissions)) {
            throw new Error('Package permissions must be an array');
        }

        this.logger.debug('ModulePackage', `Manifest validation passed for ${this.id}`);
    }

    /**
     * Validate compatibility with current environment
     * @private
     */
    async validateCompatibility() {
        // Check CoreDesk version compatibility
        if (this.dependencies.coredesk) {
            const currentVersion = window.COREDESK_CONSTANTS?.VERSION || '2.0.0';
            if (!this.isVersionCompatible(this.dependencies.coredesk, currentVersion)) {
                throw new Error(`Package requires CoreDesk ${this.dependencies.coredesk}, current: ${currentVersion}`);
            }
        }

        // Check required APIs
        const requiredAPIs = this.manifest.requiredAPIs || [];
        for (const api of requiredAPIs) {
            if (!this.isAPIAvailable(api)) {
                throw new Error(`Required API not available: ${api}`);
            }
        }

        // Check browser compatibility
        if (this.manifest.browserRequirements) {
            await this.validateBrowserRequirements();
        }
    }

    /**
     * Validate permissions required by the package
     * @private
     */
    async validatePermissions() {
        // Check if permission system is available
        const permissionManager = window.permissionManager;
        if (!permissionManager && this.requiredPermissions.length > 0) {
            throw new Error('Permission manager not available, but package requires permissions');
        }

        // Validate each required permission
        for (const permission of this.requiredPermissions) {
            if (permissionManager && !await permissionManager.hasPermission(permission)) {
                throw new Error(`Permission required: ${permission}`);
            }
        }
    }

    /**
     * Create module class from code string
     * @private
     */
    async createModuleClass() {
        try {
            // If module code is not loaded, try to load from filesystem
            if (!this.moduleCode || this.moduleCode.trim() === '') {
                await this.loadModuleFromFilesystem();
            }

            // Safety check for malicious code (basic)
            if (!this.options.allowUnsafeCode) {
                this.validateModuleCode();
            }

            // Create module class dynamically
            if (this.options.sandboxed) {
                this.moduleClass = this.createSandboxedModuleClass();
            } else {
                this.moduleClass = this.createUnsandboxedModuleClass();
            }

            // Validate created class
            if (typeof this.moduleClass !== 'function') {
                throw new Error('Module code must export a class or constructor function');
            }

            this.logger.debug('ModulePackage', `Module class created for ${this.id}`);

        } catch (error) {
            this.logger.error('ModulePackage', `Failed to create module class for ${this.id}:`, error);
            this.logger.error('ModulePackage', `Module manifest:`, this.manifest);
            this.logger.error('ModulePackage', `Module code length: ${this.moduleCode ? this.moduleCode.length : 0} characters`);
            
            // Log first 500 characters of code to help debug
            if (this.moduleCode) {
                this.logger.debug('ModulePackage', `Module code preview (first 500 chars):`);
                this.logger.debug('ModulePackage', this.moduleCode.substring(0, 500));
            }
            
            throw new Error(`Invalid module code: ${error.message}`);
        }
    }

    /**
     * Load module code from filesystem
     * @private
     */
    async loadModuleFromFilesystem() {
        try {
            // Get CoreDesk path and construct module file path
            const coreDeskPathResult = await window.electronAPI.fileSystem.getCoreDeskPath();
            if (!coreDeskPathResult.success) {
                throw new Error(`Failed to get CoreDesk path: ${coreDeskPathResult.error}`);
            }
            const coreDeskPath = coreDeskPathResult.path;
            const moduleFilePath = `${coreDeskPath}/modulos/${this.id}/${this.id}.js`;

            // Check if module file exists
            const fileExists = await window.electronAPI.fileSystem.exists(moduleFilePath);
            if (!fileExists.exists) {
                throw new Error(`Module file not found: ${moduleFilePath}`);
            }

            // Read module code from file
            const fileContent = await window.electronAPI.fileSystem.readFile(moduleFilePath);
            this.moduleCode = fileContent.content;

            this.logger.info('ModulePackage', `Module code loaded from filesystem: ${moduleFilePath}`);

            // Also load styles if available
            const stylesFilePath = `${coreDeskPath}/modulos/${this.id}/${this.id}.css`;
            const stylesExists = await window.electronAPI.fileSystem.exists(stylesFilePath);
            if (stylesExists.exists) {
                const stylesContent = await window.electronAPI.fileSystem.readFile(stylesFilePath);
                this.styles = stylesContent.content;
                this.logger.info('ModulePackage', `Module styles loaded from filesystem: ${stylesFilePath}`);
            }

            // Load manifest if available
            const manifestFilePath = `${coreDeskPath}/modulos/${this.id}/manifest.json`;
            const manifestExists = await window.electronAPI.fileSystem.exists(manifestFilePath);
            if (manifestExists.exists) {
                const manifestContent = await window.electronAPI.fileSystem.readFile(manifestFilePath);
                this.manifest = JSON.parse(manifestContent.content);
                this.logger.info('ModulePackage', `Module manifest loaded from filesystem: ${manifestFilePath}`);
            }

        } catch (error) {
            this.logger.error('ModulePackage', `Failed to load module from filesystem: ${error.message}`);
            throw error;
        }
    }

    /**
     * Create unsandboxed module class (standard execution)
     * @private
     */
    createUnsandboxedModuleClass() {
        // Validate that we have module code
        if (!this.moduleCode || this.moduleCode.trim() === '') {
            throw new Error('Module code is empty or missing');
        }

        // Clean up the module code by removing Windows line endings
        this.moduleCode = this.moduleCode.replace(/\r\n/g, '\n').replace(/\r/g, '\n');

        // CSP-Safe approach: Check if moduleCode is a simple global reference
        if (this.moduleCode && this.moduleCode.startsWith('window.')) {
            const globalRef = this.moduleCode.trim();
            this.logger.warn('ModulePackage', `Using deprecated window reference: ${globalRef}. Consider updating to proper module code.`);
            
            try {
                // Safely access global reference without eval
                const parts = globalRef.split('.');
                let obj = window;
                for (let i = 1; i < parts.length; i++) { // Skip 'window'
                    obj = obj[parts[i]];
                    if (!obj) {
                        throw new Error(`Global reference ${globalRef} not found in window scope`);
                    }
                }
                
                if (typeof obj === 'function') {
                    return obj;
                } else {
                    throw new Error(`${globalRef} is not a constructor function`);
                }
            } catch (error) {
                throw new Error(`Failed to resolve global reference ${globalRef}: ${error.message}`);
            }
        }

        // Extract class name from manifest.main (remove .js extension if present)
        const className = this.manifest.main ? this.manifest.main.replace('.js', '') : 'Module';
        
        // Try to execute module code and extract class
        try {
            // Log module code for debugging
            this.logger.debug('ModulePackage', `Attempting to execute module code for ${this.id}:`);
            this.logger.debug('ModulePackage', `Module code length: ${this.moduleCode.length} characters`);
            this.logger.debug('ModulePackage', `Expected class name: ${className}`);

            // First attempt: Try to execute the code directly if it's a class definition
            if (this.moduleCode.includes('class ') || this.moduleCode.includes('function ')) {
                // Execute the module code in a controlled environment
                let ModuleClass;

                try {
                    // Validate module code syntax before execution
                    try {
                        new Function(this.moduleCode);
                        this.logger.debug('ModulePackage', 'Module code syntax validation passed');
                    } catch (syntaxError) {
                        this.logger.error('ModulePackage', `Module code syntax error: ${syntaxError.message}`);
                        throw new Error(`Module code has syntax errors: ${syntaxError.message}`);
                    }

                    // Create a new function that executes the module code and extracts the class
                    const moduleExecutor = new Function(`
                        ${this.moduleCode}

                        // Try to find the main class
                        if (typeof ${className} !== 'undefined') {
                            return ${className};
                        }

                        // Look for any class definition in the code
                        const classRegex = /class\\s+(\\w+)/g;
                        let match;
                        const moduleCodeStr = ${JSON.stringify(this.moduleCode)};
                        while ((match = classRegex.exec(moduleCodeStr)) !== null) {
                            const foundClassName = match[1];
                            try {
                                if (typeof eval(foundClassName) !== 'undefined') {
                                    return eval(foundClassName);
                                }
                            } catch (e) {
                                // Continue searching
                            }
                        }

                        throw new Error('No main class found: ${className}');
                    `);

                    ModuleClass = moduleExecutor();
                    this.logger.debug('ModulePackage', `Successfully executed module code and found class: ${typeof ModuleClass}`);
                } catch (executionError) {
                    this.logger.error('ModulePackage', `Module execution error: ${executionError.message}`);
                    throw new Error(`Failed to execute module code: ${executionError.message}`);
                }
                
                // Validate that the returned value is a constructor function
                if (typeof ModuleClass !== 'function') {
                    throw new Error(`Module code must export a class or constructor function, got: ${typeof ModuleClass}`);
                }
                
                return ModuleClass;
            } else {
                throw new Error('Module code does not contain a class or function definition');
            }
        } catch (error) {
            // Provide detailed error information
            this.logger.error('ModulePackage', `Module execution failed for ${this.id}:`, error);
            this.logger.error('ModulePackage', `Error type: ${error.name}`);
            this.logger.error('ModulePackage', `Error message: ${error.message}`);
            this.logger.error('ModulePackage', `Error stack: ${error.stack}`);
            
            // Log the full module code for debugging
            this.logger.debug('ModulePackage', `Full module code for ${this.id}:`);
            this.logger.debug('ModulePackage', '--- MODULE CODE START ---');
            this.logger.debug('ModulePackage', this.moduleCode);
            this.logger.debug('ModulePackage', '--- MODULE CODE END ---');
            
            // Check for common issues
            if (error.message.includes('Invalid or unexpected token')) {
                this.logger.error('ModulePackage', 'This error often indicates template literals or syntax issues in the module code');
            }
            
            throw new Error(`Module execution failed: ${error.message}`);
        }
    }

    /**
     * Create sandboxed module class (limited execution context)
     * @private
     */
    createSandboxedModuleClass() {
        // Create limited context for sandboxed execution
        const sandbox = {
            console: console,
            window: {
                // Limited window access
                COREDESK_CONSTANTS: window.COREDESK_CONSTANTS,
                GlobalLogger: window.GlobalLogger
            },
            // Add other safe globals as needed
        };

        // This is a simplified sandbox - in production, use a proper sandboxing library
        const sandboxedCode = `
            (function(sandbox) {
                with(sandbox) {
                    ${this.moduleCode}
                    return ${this.manifest.main};
                }
            })(arguments[0]);
        `;

        try {
            return new Function(sandboxedCode)(sandbox);
        } catch (error) {
            throw new Error(`Sandboxed module execution failed: ${error.message}`);
        }
    }

    /**
     * Validate module code for security
     * @private
     */
    validateModuleCode() {
        const dangerousPatterns = [
            /eval\s*\(/,
            /Function\s*\(/,
            /document\.write/,
            /\.innerHTML\s*=/,
            /import\s+.*\s+from/,
            /require\s*\(/,
            /process\./,
            /fs\./,
            /child_process/
        ];

        for (const pattern of dangerousPatterns) {
            if (pattern.test(this.moduleCode)) {
                throw new Error(`Potentially dangerous code detected: ${pattern}`);
            }
        }
    }

    /**
     * Install package assets
     * @private
     */
    async installAssets() {
        try {
            // Get CoreDesk path and create module directory
            const coreDeskPathResult = await window.electronAPI.fileSystem.getCoreDeskPath();
            if (!coreDeskPathResult.success) {
                throw new Error(`Failed to get CoreDesk path: ${coreDeskPathResult.error}`);
            }
            const coreDeskPath = coreDeskPathResult.path;
            this.installPath = `${coreDeskPath}/modulos/${this.id}`;

            // Create module directory
            await window.electronAPI.fileSystem.createDirectory(this.installPath);

            // Write module code to file
            if (this.moduleCode) {
                const moduleFilePath = `${this.installPath}/${this.id}.js`;
                await window.electronAPI.fileSystem.writeFile(moduleFilePath, this.moduleCode);
                this.logger.info('ModulePackage', `Module code written to: ${moduleFilePath}`);
            }

            // Write styles to file
            if (this.styles) {
                const stylesFilePath = `${this.installPath}/${this.id}.css`;
                await window.electronAPI.fileSystem.writeFile(stylesFilePath, this.styles);
                this.logger.info('ModulePackage', `Styles written to: ${stylesFilePath}`);
            }

            // Write manifest to file
            if (this.manifest) {
                const manifestFilePath = `${this.installPath}/manifest.json`;
                await window.electronAPI.fileSystem.writeFile(manifestFilePath, JSON.stringify(this.manifest, null, 2));
                this.logger.info('ModulePackage', `Manifest written to: ${manifestFilePath}`);
            }

            // Process other assets (icons, templates, etc.)
            if (this.assets) {
                await this.processOtherAssets();
            }

            this.logger.info('ModulePackage', `Assets installed for ${this.id} at: ${this.installPath}`);

        } catch (error) {
            throw new Error(`Asset installation failed: ${error.message}`);
        }
    }

    /**
     * Process CSS assets
     * @private
     */
    async processStyleAssets() {
        // Process CSS for relative URLs, etc.
        const processedCSS = this.styles.replace(
            /url\(['"]?([^'"]*?)['"]?\)/g,
            (match, url) => {
                if (url.startsWith('http') || url.startsWith('data:')) {
                    return match; // External or data URLs
                }
                return `url('${this.installPath}/assets/${url}')`;
            }
        );

        this.styles = processedCSS;
    }

    /**
     * Process other assets
     * @private
     */
    async processOtherAssets() {
        // In a real implementation, this would handle
        // icons, templates, and other asset types
        this.logger.debug('ModulePackage', `Processing ${Object.keys(this.assets).length} asset types`);
    }

    /**
     * Remove installed assets
     * @private
     */
    async removeAssets() {
        // Clean up any installed assets
        // In browser environment, this might involve removing cached data
        this.logger.debug('ModulePackage', `Removing assets for ${this.id}`);
    }

    /**
     * Update local metadata storage
     * @private
     */
    async updateLocalMetadata() {
        try {
            const metadata = this.getMetadata();
            const storageKey = `coredesk_module_${this.id}`;
            
            // Store complete package data for proper reconstruction
            const completePackageData = {
                ...metadata,
                installedAt: this.installedAt,
                // Store original package data for reconstruction
                originalPackageData: {
                    manifest: this.manifest,
                    moduleCode: this.moduleCode,
                    styles: this.styles,
                    assets: this.assets
                },
                // Store module type information
                moduleType: this.isBinary ? 'binary' : 'json',
                binarySize: this.binaryData ? this.binaryData.size : 0
            };
            
            this.logger.info('ModulePackage', `About to store data with key: ${storageKey}`);
            localStorage.setItem(storageKey, JSON.stringify(completePackageData));
            this.logger.info('ModulePackage', `Successfully stored complete package data for ${this.id}`);
            
            // Verify the data was stored
            const verification = localStorage.getItem(storageKey);
            this.logger.info('ModulePackage', `Verification - data exists in localStorage: ${verification ? 'YES' : 'NO'}`);

        } catch (error) {
            this.logger.warn('ModulePackage', `Failed to update local metadata for ${this.id}:`, error);
        }
    }

    /**
     * Clear local metadata storage
     * @private
     */
    async clearLocalMetadata() {
        try {
            const storageKey = `coredesk_module_${this.id}`;
            localStorage.removeItem(storageKey);
        } catch (error) {
            this.logger.warn('ModulePackage', `Failed to clear local metadata for ${this.id}:`, error);
        }
    }

    /**
     * Cleanup after failed installation
     * @private
     */
    async cleanupFailedInstallation() {
        try {
            await this.removeAssets();
            await this.clearLocalMetadata();
            this.moduleClass = null;
            this.moduleInstance = null;
        } catch (error) {
            this.logger.error('ModulePackage', `Cleanup failed for ${this.id}:`, error);
        }
    }

    /**
     * Cleanup module instance
     * @private
     */
    async cleanupModuleInstance() {
        try {
            if (this.moduleInstance) {
                // Call cleanup method if available
                if (typeof this.moduleInstance.cleanup === 'function') {
                    await this.moduleInstance.cleanup();
                }

                // Call deactivate if available
                if (typeof this.moduleInstance.deactivate === 'function') {
                    await this.moduleInstance.deactivate();
                }

                this.moduleInstance = null;
                this.isLoaded = false;
            }
        } catch (error) {
            this.logger.error('ModulePackage', `Module instance cleanup failed for ${this.id}:`, error);
        }
    }

    /**
     * Check version compatibility
     * @private
     */
    isVersionCompatible(required, current) {
        // Simple version comparison - could be enhanced with semver
        if (required.startsWith('>=')) {
            return current >= required.slice(2);
        }
        if (required.startsWith('^')) {
            const baseVersion = required.slice(1);
            const [reqMajor] = baseVersion.split('.');
            const [curMajor] = current.split('.');
            return curMajor === reqMajor && current >= baseVersion;
        }
        return current === required;
    }

    /**
     * Check if API is available
     * @private
     */
    isAPIAvailable(apiName) {
        const apiMap = {
            'database': () => window.databaseManager,
            'license': () => window.licenseManager,
            'filesystem': () => window.fileSystem,
            'notifications': () => window.notifications,
            'events': () => window.CoreDeskEvents
        };

        const checker = apiMap[apiName];
        return checker ? !!checker() : false;
    }

    /**
     * Validate browser requirements
     * @private
     */
    async validateBrowserRequirements() {
        const requirements = this.manifest.browserRequirements;
        
        // Check minimum browser versions, required features, etc.
        if (requirements.minChrome && this.getBrowserVersion('chrome') < requirements.minChrome) {
            throw new Error(`Chrome ${requirements.minChrome} or higher required`);
        }

        // Check for required browser features
        if (requirements.features) {
            for (const feature of requirements.features) {
                if (!this.isBrowserFeatureSupported(feature)) {
                    throw new Error(`Browser feature not supported: ${feature}`);
                }
            }
        }
    }

    /**
     * Get browser version (simplified)
     * @private
     */
    getBrowserVersion(browser) {
        // Simplified browser detection
        const userAgent = navigator.userAgent;
        if (browser === 'chrome' && userAgent.includes('Chrome/')) {
            const match = userAgent.match(/Chrome\/(\d+)/);
            return match ? parseInt(match[1]) : 0;
        }
        return 0;
    }

    /**
     * Check if browser feature is supported
     * @private
     */
    isBrowserFeatureSupported(feature) {
        const featureMap = {
            'es6': () => typeof Symbol !== 'undefined',
            'modules': () => 'noModule' in document.createElement('script'),
            'serviceWorker': () => 'serviceWorker' in navigator,
            'indexedDB': () => 'indexedDB' in window,
            'localStorage': () => 'localStorage' in window
        };

        const checker = featureMap[feature];
        return checker ? checker() : false;
    }

    /**
     * Get package size estimation
     * @private
     */
    getPackageSize() {
        const codeSize = new Blob([this.moduleCode]).size;
        const styleSize = new Blob([this.styles]).size;
        const assetSize = JSON.stringify(this.assets).length;
        
        return codeSize + styleSize + assetSize;
    }
}

// Make available globally
window.ModulePackage = ModulePackage;

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ModulePackage;
}

console.log('ModulePackage', '[ModulePackage] Class loaded successfully');