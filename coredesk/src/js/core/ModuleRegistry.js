/**
 * ModuleRegistry
 * Central registry for managing installed and available modules in CoreDesk Framework v2.0.0
 * 
 * Features:
 * - Track installed modules and their metadata
 * - Manage module dependencies and relationships
 * - Provide query interface for modules
 * - Handle module state persistence
 * - Support for module categories and tagging
 * - Integration with license validation
 */

class ModuleRegistry extends EventTarget {
    constructor(options = {}) {
        super();
        
        // Configuration
        this.options = {
            persistenceEnabled: true,
            storagePrefix: 'coredesk_registry_',
            cacheTTL: 24 * 60 * 60 * 1000, // 24 hours
            autoCleanup: true,
            validateIntegrity: true,
            ...options
        };
        
        // Registry state
        this.modules = new Map();              // moduleId -> ModulePackage
        this.moduleMetadata = new Map();       // moduleId -> metadata
        this.dependencies = new Map();         // moduleId -> Set of dependent moduleIds
        this.categories = new Map();           // category -> Set of moduleIds
        this.tags = new Map();                 // tag -> Set of moduleIds
        
        // Cache and indexing
        this.searchIndex = new Map();          // search term -> Set of moduleIds
        this.lastUpdated = null;
        this.isInitialized = false;
        
        // Storage interface
        this.storage = this.createStorageInterface();
        
        // Logger
        this.logger = window.GlobalLogger || {
            info: (tag, message) => console.log(`[${tag}] ${message}`),
            error: (tag, message, error) => console.error(`[${tag}] ${message}`, error),
            warn: (tag, message) => console.warn(`[${tag}] ${message}`),
            debug: (tag, message) => console.debug(`[${tag}] ${message}`)
        };
    }

    /**
     * Initialize the module registry
     * @returns {Promise<void>}
     */
    async initialize() {
        try {
            this.logger.info('ModuleRegistry', 'Initializing module registry...');

            // Load persisted registry data
            if (this.options.persistenceEnabled) {
                // Add a small delay to ensure app is initialized
                await new Promise(resolve => setTimeout(resolve, 100));
                await this.loadPersistedData();
            }

            // Build search index
            this.buildSearchIndex();

            // Setup cleanup if enabled
            if (this.options.autoCleanup) {
                this.setupAutoCleanup();
            }

            this.isInitialized = true;
            this.lastUpdated = new Date();

            this.logger.info('ModuleRegistry', `Registry initialized with ${this.modules.size} modules`);
            this.dispatchEvent(new CustomEvent('registryInitialized', {
                detail: { moduleCount: this.modules.size }
            }));

        } catch (error) {
            this.logger.error('ModuleRegistry', 'Failed to initialize registry:', error);
            throw new Error(`ModuleRegistry initialization failed: ${error.message}`);
        }
    }

    /**
     * Register a module package
     * @param {ModulePackage} modulePackage - Module package to register
     * @returns {Promise<void>}
     */
    async registerModule(modulePackage) {
        try {
            if (!modulePackage || !modulePackage.id) {
                throw new Error('Invalid module package');
            }

            const moduleId = modulePackage.id;
            this.logger.info('ModuleRegistry', `Registering module: ${moduleId}`);

            // Validate module package
            await this.validateModulePackage(modulePackage);

            // Check for conflicts
            if (this.modules.has(moduleId)) {
                const existingVersion = this.modules.get(moduleId).version;
                if (!this.isVersionNewer(modulePackage.version, existingVersion)) {
                    throw new Error(`Module ${moduleId} v${existingVersion} is already registered`);
                }
                
                // Unregister old version first
                await this.unregisterModule(moduleId, { skipValidation: true });
            }

            // Store module package
            this.modules.set(moduleId, modulePackage);

            // Store metadata
            const metadata = this.extractModuleMetadata(modulePackage);
            this.moduleMetadata.set(moduleId, metadata);

            // Update dependency tracking
            await this.updateDependencyTracking(moduleId, modulePackage);

            // Update category and tag indexing
            this.updateCategoryIndex(moduleId, metadata);
            this.updateTagIndex(moduleId, metadata);

            // Update search index
            this.updateSearchIndex(moduleId, metadata);

            // Persist changes
            if (this.options.persistenceEnabled) {
                await this.persistModuleData(moduleId, metadata);
            }

            this.lastUpdated = new Date();

            this.logger.info('ModuleRegistry', `Module ${moduleId} registered successfully`);
            this.dispatchEvent(new CustomEvent('moduleRegistered', {
                detail: { moduleId, metadata }
            }));

        } catch (error) {
            this.logger.error('ModuleRegistry', `Failed to register module:`, error);
            throw error;
        }
    }

    /**
     * Unregister a module
     * @param {string} moduleId - Module identifier
     * @param {Object} options - Unregistration options
     * @returns {Promise<void>}
     */
    async unregisterModule(moduleId, options = {}) {
        try {
            const { skipValidation = false, force = false } = options;

            this.logger.info('ModuleRegistry', `Unregistering module: ${moduleId}`);

            if (!this.modules.has(moduleId)) {
                throw new Error(`Module ${moduleId} is not registered`);
            }

            // Check dependencies if not forcing
            if (!force && !skipValidation) {
                const dependents = this.getModulesDependingOn(moduleId);
                if (dependents.length > 0) {
                    throw new Error(`Cannot unregister ${moduleId}. Required by: ${dependents.join(', ')}`);
                }
            }

            // Remove from all indexes
            this.removeFromCategoryIndex(moduleId);
            this.removeFromTagIndex(moduleId);
            this.removeFromSearchIndex(moduleId);
            this.removeDependencyTracking(moduleId);

            // Remove from main storage
            this.modules.delete(moduleId);
            this.moduleMetadata.delete(moduleId);

            // Remove persisted data
            if (this.options.persistenceEnabled) {
                await this.removePersistedModuleData(moduleId);
            }

            this.lastUpdated = new Date();

            this.logger.info('ModuleRegistry', `Module ${moduleId} unregistered successfully`);
            this.dispatchEvent(new CustomEvent('moduleUnregistered', {
                detail: { moduleId }
            }));

        } catch (error) {
            this.logger.error('ModuleRegistry', `Failed to unregister module ${moduleId}:`, error);
            throw error;
        }
    }

    /**
     * Get a registered module package
     * @param {string} moduleId - Module identifier
     * @returns {ModulePackage|null}
     */
    getModule(moduleId) {
        return this.modules.get(moduleId) || null;
    }

    /**
     * Get module metadata
     * @param {string} moduleId - Module identifier
     * @returns {Object|null}
     */
    getModuleMetadata(moduleId) {
        return this.moduleMetadata.get(moduleId) || null;
    }

    /**
     * Get all installed module IDs
     * @returns {Array<string>}
     */
    getInstalledModuleIds() {
        return Array.from(this.modules.keys());
    }

    /**
     * Get all installed modules
     * @returns {Array<ModulePackage>}
     */
    getInstalledModules() {
        return Array.from(this.modules.values());
    }

    /**
     * Get modules by category
     * @param {string} category - Category name
     * @returns {Array<string>}
     */
    getModulesByCategory(category) {
        return Array.from(this.categories.get(category) || []);
    }

    /**
     * Get modules by tag
     * @param {string} tag - Tag name
     * @returns {Array<string>}
     */
    getModulesByTag(tag) {
        return Array.from(this.tags.get(tag) || []);
    }

    /**
     * Search modules
     * @param {string} query - Search query
     * @param {Object} filters - Additional filters
     * @returns {Array<Object>}
     */
    searchModules(query, filters = {}) {
        const results = [];
        const queryLower = query.toLowerCase();

        for (const [moduleId, metadata] of this.moduleMetadata) {
            let score = 0;

            // Name match (highest score)
            if (metadata.name.toLowerCase().includes(queryLower)) {
                score += 10;
            }

            // ID match
            if (metadata.id.toLowerCase().includes(queryLower)) {
                score += 8;
            }

            // Description match
            if (metadata.description && metadata.description.toLowerCase().includes(queryLower)) {
                score += 5;
            }

            // Tag match
            if (metadata.tags && metadata.tags.some(tag => tag.toLowerCase().includes(queryLower))) {
                score += 3;
            }

            // Category match
            if (metadata.category && metadata.category.toLowerCase().includes(queryLower)) {
                score += 3;
            }

            // Apply filters
            if (score > 0 && this.matchesFilters(metadata, filters)) {
                results.push({
                    moduleId,
                    metadata,
                    score
                });
            }
        }

        // Sort by score (descending)
        return results.sort((a, b) => b.score - a.score);
    }

    /**
     * Get modules that depend on a specific module
     * @param {string} moduleId - Module identifier
     * @returns {Array<string>}
     */
    getModulesDependingOn(moduleId) {
        const dependents = [];

        for (const [currentModuleId, currentMetadata] of this.moduleMetadata) {
            if (currentMetadata.dependencies && currentMetadata.dependencies[moduleId]) {
                dependents.push(currentModuleId);
            }
        }

        return dependents;
    }

    /**
     * Get module dependency chain
     * @param {string} moduleId - Module identifier
     * @returns {Object}
     */
    getModuleDependencies(moduleId) {
        const metadata = this.moduleMetadata.get(moduleId);
        if (!metadata || !metadata.dependencies) {
            return { dependencies: [], missing: [] };
        }

        const dependencies = [];
        const missing = [];

        for (const [depId, version] of Object.entries(metadata.dependencies)) {
            if (depId === 'coredesk') continue; // Skip core dependency

            if (this.modules.has(depId)) {
                const depMetadata = this.moduleMetadata.get(depId);
                dependencies.push({
                    id: depId,
                    requiredVersion: version,
                    installedVersion: depMetadata.version,
                    compatible: this.isVersionCompatible(version, depMetadata.version)
                });
            } else {
                missing.push({
                    id: depId,
                    requiredVersion: version
                });
            }
        }

        return { dependencies, missing };
    }

    /**
     * Check if a module is registered
     * @param {string} moduleId - Module identifier
     * @returns {boolean}
     */
    isModuleRegistered(moduleId) {
        return this.modules.has(moduleId);
    }

    /**
     * Get registry statistics
     * @returns {Object}
     */
    getRegistryStats() {
        const stats = {
            totalModules: this.modules.size,
            categories: {},
            tags: {},
            versions: {},
            lastUpdated: this.lastUpdated
        };

        // Count by category
        for (const [category, moduleSet] of this.categories) {
            stats.categories[category] = moduleSet.size;
        }

        // Count by tag
        for (const [tag, moduleSet] of this.tags) {
            stats.tags[tag] = moduleSet.size;
        }

        // Count by version
        for (const metadata of this.moduleMetadata.values()) {
            const majorVersion = metadata.version.split('.')[0];
            stats.versions[majorVersion] = (stats.versions[majorVersion] || 0) + 1;
        }

        return stats;
    }

    /**
     * Export registry data
     * @returns {Object}
     */
    exportRegistry() {
        const exported = {
            version: '1.0.0',
            exportedAt: new Date().toISOString(),
            modules: {},
            metadata: Object.fromEntries(this.moduleMetadata)
        };

        // Include module data (excluding actual code for security)
        for (const [moduleId, modulePackage] of this.modules) {
            exported.modules[moduleId] = {
                manifest: modulePackage.manifest,
                isInstalled: modulePackage.isInstalled,
                installedAt: modulePackage.installedAt
            };
        }

        return exported;
    }

    /**
     * Import registry data
     * @param {Object} registryData - Registry data to import
     * @returns {Promise<void>}
     */
    async importRegistry(registryData) {
        try {
            this.logger.info('ModuleRegistry', 'Importing registry data...');

            if (!registryData.version || !registryData.modules) {
                throw new Error('Invalid registry data format');
            }

            // Clear current registry
            this.modules.clear();
            this.moduleMetadata.clear();
            this.clearAllIndexes();

            // Import metadata
            for (const [moduleId, metadata] of Object.entries(registryData.metadata)) {
                this.moduleMetadata.set(moduleId, metadata);
                this.updateCategoryIndex(moduleId, metadata);
                this.updateTagIndex(moduleId, metadata);
                this.updateSearchIndex(moduleId, metadata);
            }

            // Rebuild search index
            this.buildSearchIndex();

            this.lastUpdated = new Date();

            this.logger.info('ModuleRegistry', `Registry imported with ${this.moduleMetadata.size} modules`);

        } catch (error) {
            this.logger.error('ModuleRegistry', 'Failed to import registry:', error);
            throw error;
        }
    }

    // Private Methods

    /**
     * Create storage interface
     * @private
     */
    createStorageInterface() {
        return {
            getItem: (key) => localStorage.getItem(key),
            setItem: (key, value) => localStorage.setItem(key, value),
            removeItem: (key) => localStorage.removeItem(key),
            getAllKeys: () => Object.keys(localStorage).filter(key => 
                key.startsWith(this.options.storagePrefix)
            )
        };
    }

    /**
     * Load persisted registry data
     * @private
     */
    async loadPersistedData() {
        try {
            const registryKey = `${this.options.storagePrefix}registry`;
            const registryData = this.storage.getItem(registryKey);

            if (registryData) {
                const parsed = JSON.parse(registryData);
                
                // Check if data is still valid (TTL)
                if (this.isDataValid(parsed)) {
                    // Load metadata
                    for (const [moduleId, metadata] of Object.entries(parsed.metadata || {})) {
                        this.moduleMetadata.set(moduleId, metadata);
                        this.updateCategoryIndex(moduleId, metadata);
                        this.updateTagIndex(moduleId, metadata);
                    }

                    this.logger.debug('ModuleRegistry', 'Loaded persisted registry data');
                } else {
                    this.logger.warn('ModuleRegistry', 'Persisted registry data expired');
                }
            }

            // Also load installed modules from individual storage keys
            await this.loadInstalledModulesFromStorage();

        } catch (error) {
            this.logger.warn('ModuleRegistry', 'Failed to load persisted data:', error);
        }
    }

    /**
     * Load installed modules from their individual storage keys
     * @private
     */
    async loadInstalledModulesFromStorage() {
        try {
            // Check filesystem for installed modules
            const modulesPathResult = await window.electronAPI.fileSystem.getModulesPath();
            if (!modulesPathResult.success) {
                throw new Error(`Failed to get modules path: ${modulesPathResult.error}`);
            }
            const modulesPath = modulesPathResult.path;

            // Check if modules directory exists
            const modulesDirExists = await window.electronAPI.fileSystem.exists(modulesPath);
            if (!modulesDirExists.exists) {
                this.logger.info('ModuleRegistry', 'No modulos directory found - creating it');
                await window.electronAPI.fileSystem.createDirectory(modulesPath);
                return;
            }

            // List module directories
            const moduleDirs = await window.electronAPI.fileSystem.listDirectory(modulesPath);
            const moduleIds = moduleDirs.files.filter(file => file.isDirectory).map(dir => dir.name);
            
            this.logger.info('ModuleRegistry', `Found ${moduleIds.length} module directories: ${moduleIds.join(', ')}`);
            
            for (const moduleId of moduleIds) {
                try {
                    const moduleDir = `${modulesPath}/${moduleId}`;
                    
                    // Check if module has required files
                    const moduleFile = `${moduleDir}/${moduleId}.js`;
                    const manifestFile = `${moduleDir}/manifest.json`;
                    
                    const moduleFileExists = await window.electronAPI.fileSystem.exists(moduleFile);
                    const manifestFileExists = await window.electronAPI.fileSystem.exists(manifestFile);
                    
                    if (moduleFileExists.exists && manifestFileExists.exists) {
                        // Load manifest
                        const manifestContent = await window.electronAPI.fileSystem.readFile(manifestFile);
                        const manifest = JSON.parse(manifestContent.content);
                        
                        // Load module code
                        const moduleContent = await window.electronAPI.fileSystem.readFile(moduleFile);
                        const moduleCode = moduleContent.content;
                        
                        // Load styles if available
                        const stylesFile = `${moduleDir}/${moduleId}.css`;
                        const stylesFileExists = await window.electronAPI.fileSystem.exists(stylesFile);
                        let styles = '';
                        if (stylesFileExists.exists) {
                            const stylesContent = await window.electronAPI.fileSystem.readFile(stylesFile);
                            styles = stylesContent.content;
                        }
                        
                        // Recreate the module class
                        await this.recreateModuleClass(moduleId, { manifest, moduleCode });
                        
                        // Create package data for registration
                        const packageData = {
                            manifest: manifest,
                            moduleCode: moduleCode,
                            styles: styles,
                            assets: {}
                        };
                        
                        // Create ModulePackage instance
                        if (window.ModulePackage) {
                            const modulePackage = new window.ModulePackage(packageData, {
                                validateManifest: true,
                                strictMode: false,
                                allowUnsafeCode: true
                            });
                            
                            // Mark as installed and set install path
                            modulePackage.isInstalled = true;
                            modulePackage.isInitialized = true;
                            modulePackage.installPath = moduleDir;
                            modulePackage.installedAt = new Date().toISOString();
                            
                            // Store in registry
                            this.modules.set(moduleId, modulePackage);
                            
                            this.logger.info('ModuleRegistry', `Successfully loaded installed module: ${moduleId}`);
                        }
                    } else {
                        this.logger.warn('ModuleRegistry', `Module ${moduleId} missing required files - skipping`);
                    }
                } catch (moduleError) {
                    this.logger.error('ModuleRegistry', `Failed to load module ${moduleId}:`, moduleError);
                }
            }
            
            this.logger.info('ModuleRegistry', `Loaded ${this.modules.size} installed modules from filesystem`);
            
        } catch (error) {
            this.logger.error('ModuleRegistry', 'Failed to load installed modules from filesystem:', error);
        }
    }

    /**
     * Recreate module class for persisted modules
     * @private
     */
    async recreateModuleClass(moduleId, moduleData) {
        const className = `${moduleId.charAt(0).toUpperCase() + moduleId.slice(1)}Module`;
        
        // Check if class already exists
        if (window[className]) {
            this.logger.debug('ModuleRegistry', `Module class ${className} already exists`);
            return;
        }
        
        // Get access to the app's createCSPSafeModuleClass method
        const app = window.coreDeskApp || window.coreDesk?.app || window.CoreDeskAppInstance;
        if (app && typeof app.createCSPSafeModuleClass === 'function') {
            const moduleType = moduleData.moduleType || 'json';
            const size = moduleData.binarySize || 0;
            
            // Recreate the module class
            const moduleCodeRef = app.createCSPSafeModuleClass(moduleId, moduleType, size);
            this.logger.info('ModuleRegistry', `Recreated module class: ${moduleCodeRef}`);
            
            // Verify the class was created
            if (!window[className]) {
                this.logger.error('ModuleRegistry', `Failed to create module class ${className} on window`);
            } else {
                this.logger.info('ModuleRegistry', `Module class ${className} successfully attached to window`);
            }
        } else {
            this.logger.warn('ModuleRegistry', `Cannot recreate module class for ${moduleId} - createCSPSafeModuleClass not available`);
            
            // Fallback: Try to recreate the module class directly
            this.logger.info('ModuleRegistry', `Attempting fallback module class creation for ${moduleId}`);
            this.createFallbackModuleClass(moduleId, moduleData);
        }
    }
    
    /**
     * Create a fallback module class when app.createCSPSafeModuleClass is not available
     * @private
     */
    createFallbackModuleClass(moduleId, moduleData) {
        const className = `${moduleId.charAt(0).toUpperCase() + moduleId.slice(1)}Module`;
        const moduleType = moduleData.moduleType || 'json';
        const size = moduleData.binarySize || 0;
        
        // Create the module constructor
        const ModuleConstructor = function(packageData) {
            this.packageData = packageData;
            this.name = moduleId;
            this.isLoaded = false;
            console.log(`${moduleType} module ${moduleId} constructor called`);
        };
        
        ModuleConstructor.prototype.initialize = async function() {
            console.log(`${moduleType} module ${moduleId} initialized successfully`);
            this.isLoaded = true;
            return true;
        };
        
        ModuleConstructor.prototype.activate = async function() {
            console.log(`${moduleType} module ${moduleId} activated`);
            this.isActive = true;
            return true;
        };
        
        ModuleConstructor.prototype.deactivate = async function() {
            console.log(`${moduleType} module ${moduleId} deactivated`);
            this.isActive = false;
            return true;
        };
        
        ModuleConstructor.prototype.render = function() {
            const moduleContent = document.createElement('div');
            moduleContent.className = 'module-content';
            
            let headerContent = '<div class="module-header">' +
                '<h2>' + (this.name.charAt(0).toUpperCase() + this.name.slice(1)) + ' Module</h2>' +
                '<p>Type: ' + moduleType + '</p>';
            
            if (moduleType === 'binary') {
                headerContent += '<p>Size: ' + size + ' bytes</p>';
            }
            headerContent += '</div>';
            
            const bodyContent = '<div class="module-body">' +
                '<p>🚀 Module ' + this.name + ' is now active and running!</p>' +
                '<div class="module-features">' +
                    '<h3>Features:</h3>' +
                    '<ul>' +
                        '<li>✅ Successfully installed</li>' +
                        '<li>✅ Persistence enabled</li>' +
                        '<li>✅ Ready for use</li>' +
                    '</ul>' +
                '</div>' +
            '</div>';
            
            moduleContent.innerHTML = headerContent + bodyContent;
            return moduleContent;
        };
        
        ModuleConstructor.prototype.getInfo = function() {
            const info = {
                name: this.name,
                type: moduleType,
                isLoaded: this.isLoaded,
                isActive: this.isActive || false
            };
            
            if (moduleType === 'binary') {
                info.size = size;
            }
            
            return info;
        };
        
        // Attach to window for CSP-safe access
        window[className] = ModuleConstructor;
        
        this.logger.info('ModuleRegistry', `Fallback module class ${className} created and attached to window`);
    }

    /**
     * Persist module data
     * @private
     */
    async persistModuleData(moduleId, metadata) {
        try {
            const registryKey = `${this.options.storagePrefix}registry`;
            const registryData = {
                version: '1.0.0',
                timestamp: Date.now(),
                metadata: Object.fromEntries(this.moduleMetadata)
            };

            this.storage.setItem(registryKey, JSON.stringify(registryData));

        } catch (error) {
            this.logger.warn('ModuleRegistry', 'Failed to persist module data:', error);
        }
    }

    /**
     * Remove persisted module data
     * @private
     */
    async removePersistedModuleData(moduleId) {
        try {
            // Update registry data without this module
            const registryKey = `${this.options.storagePrefix}registry`;
            const registryData = {
                version: '1.0.0',
                timestamp: Date.now(),
                metadata: Object.fromEntries(this.moduleMetadata)
            };

            this.storage.setItem(registryKey, JSON.stringify(registryData));

        } catch (error) {
            this.logger.warn('ModuleRegistry', 'Failed to remove persisted data:', error);
        }
    }

    /**
     * Extract module metadata
     * @private
     */
    extractModuleMetadata(modulePackage) {
        return {
            id: modulePackage.id,
            name: modulePackage.name,
            version: modulePackage.version,
            description: modulePackage.description,
            author: modulePackage.manifest.author,
            license: modulePackage.manifest.license,
            category: modulePackage.manifest.category || 'general',
            tags: modulePackage.manifest.tags || [],
            dependencies: modulePackage.dependencies,
            permissions: modulePackage.requiredPermissions,
            requiredLicense: modulePackage.requiredLicense,
            size: modulePackage.getPackageSize ? modulePackage.getPackageSize() : 0,
            isInstalled: modulePackage.isInstalled,
            installedAt: modulePackage.installedAt,
            downloadUrl: modulePackage.manifest.downloadUrl,
            homepage: modulePackage.manifest.homepage,
            repository: modulePackage.manifest.repository
        };
    }

    /**
     * Validate module package
     * @private
     */
    async validateModulePackage(modulePackage) {
        if (!modulePackage.id || !modulePackage.version) {
            throw new Error('Module package missing required fields');
        }

        if (this.options.validateIntegrity) {
            // Additional integrity checks
            if (!modulePackage.manifest) {
                throw new Error('Module package missing manifest');
            }
        }
    }

    /**
     * Update dependency tracking
     * @private
     */
    async updateDependencyTracking(moduleId, modulePackage) {
        // Track what modules this one depends on
        if (modulePackage.dependencies) {
            const deps = new Set();
            for (const depId of Object.keys(modulePackage.dependencies)) {
                if (depId !== 'coredesk') {
                    deps.add(depId);
                }
            }
            this.dependencies.set(moduleId, deps);
        }
    }

    /**
     * Remove dependency tracking
     * @private
     */
    removeDependencyTracking(moduleId) {
        this.dependencies.delete(moduleId);
    }

    /**
     * Update category index
     * @private
     */
    updateCategoryIndex(moduleId, metadata) {
        const category = metadata.category || 'general';
        if (!this.categories.has(category)) {
            this.categories.set(category, new Set());
        }
        this.categories.get(category).add(moduleId);
    }

    /**
     * Remove from category index
     * @private
     */
    removeFromCategoryIndex(moduleId) {
        for (const [category, moduleSet] of this.categories) {
            moduleSet.delete(moduleId);
            if (moduleSet.size === 0) {
                this.categories.delete(category);
            }
        }
    }

    /**
     * Update tag index
     * @private
     */
    updateTagIndex(moduleId, metadata) {
        if (metadata.tags) {
            for (const tag of metadata.tags) {
                if (!this.tags.has(tag)) {
                    this.tags.set(tag, new Set());
                }
                this.tags.get(tag).add(moduleId);
            }
        }
    }

    /**
     * Remove from tag index
     * @private
     */
    removeFromTagIndex(moduleId) {
        for (const [tag, moduleSet] of this.tags) {
            moduleSet.delete(moduleId);
            if (moduleSet.size === 0) {
                this.tags.delete(tag);
            }
        }
    }

    /**
     * Update search index
     * @private
     */
    updateSearchIndex(moduleId, metadata) {
        const searchTerms = [
            metadata.id,
            metadata.name,
            metadata.description,
            ...(metadata.tags || []),
            metadata.category
        ].filter(Boolean);

        for (const term of searchTerms) {
            const termLower = term.toLowerCase();
            if (!this.searchIndex.has(termLower)) {
                this.searchIndex.set(termLower, new Set());
            }
            this.searchIndex.get(termLower).add(moduleId);
        }
    }

    /**
     * Remove from search index
     * @private
     */
    removeFromSearchIndex(moduleId) {
        for (const [term, moduleSet] of this.searchIndex) {
            moduleSet.delete(moduleId);
            if (moduleSet.size === 0) {
                this.searchIndex.delete(term);
            }
        }
    }

    /**
     * Build complete search index
     * @private
     */
    buildSearchIndex() {
        this.searchIndex.clear();
        for (const [moduleId, metadata] of this.moduleMetadata) {
            this.updateSearchIndex(moduleId, metadata);
        }
    }

    /**
     * Clear all indexes
     * @private
     */
    clearAllIndexes() {
        this.categories.clear();
        this.tags.clear();
        this.searchIndex.clear();
        this.dependencies.clear();
    }

    /**
     * Check if filters match metadata
     * @private
     */
    matchesFilters(metadata, filters) {
        if (filters.category && metadata.category !== filters.category) {
            return false;
        }

        if (filters.tag && (!metadata.tags || !metadata.tags.includes(filters.tag))) {
            return false;
        }

        if (filters.license && metadata.license !== filters.license) {
            return false;
        }

        return true;
    }

    /**
     * Check if version is newer
     * @private
     */
    isVersionNewer(newVersion, oldVersion) {
        const newParts = newVersion.split('.').map(Number);
        const oldParts = oldVersion.split('.').map(Number);

        for (let i = 0; i < Math.max(newParts.length, oldParts.length); i++) {
            const newPart = newParts[i] || 0;
            const oldPart = oldParts[i] || 0;

            if (newPart > oldPart) return true;
            if (newPart < oldPart) return false;
        }

        return false;
    }

    /**
     * Check if version is compatible
     * @private
     */
    isVersionCompatible(required, installed) {
        // Simple version compatibility check
        if (required.startsWith('>=')) {
            return installed >= required.slice(2);
        }
        if (required.startsWith('^')) {
            const baseVersion = required.slice(1);
            const [reqMajor] = baseVersion.split('.');
            const [instMajor] = installed.split('.');
            return instMajor === reqMajor && installed >= baseVersion;
        }
        return installed === required;
    }

    /**
     * Check if persisted data is valid
     * @private
     */
    isDataValid(data) {
        if (!data.timestamp) return false;
        const age = Date.now() - data.timestamp;
        return age < this.options.cacheTTL;
    }

    /**
     * Setup automatic cleanup
     * @private
     */
    setupAutoCleanup() {
        setInterval(() => {
            this.performCleanup();
        }, 60 * 60 * 1000); // Every hour
    }

    /**
     * Perform cleanup tasks
     * @private
     */
    performCleanup() {
        try {
            // Clean up empty index entries
            this.cleanupEmptyIndexes();

            // Clean up expired cache entries
            this.cleanupExpiredCache();

            this.logger.debug('ModuleRegistry', 'Cleanup completed');

        } catch (error) {
            this.logger.warn('ModuleRegistry', 'Cleanup failed:', error);
        }
    }

    /**
     * Clean up empty indexes
     * @private
     */
    cleanupEmptyIndexes() {
        for (const [key, set] of this.categories) {
            if (set.size === 0) {
                this.categories.delete(key);
            }
        }

        for (const [key, set] of this.tags) {
            if (set.size === 0) {
                this.tags.delete(key);
            }
        }

        for (const [key, set] of this.searchIndex) {
            if (set.size === 0) {
                this.searchIndex.delete(key);
            }
        }
    }

    /**
     * Clean up expired cache
     * @private
     */
    cleanupExpiredCache() {
        // Clean up old localStorage entries
        const allKeys = this.storage.getAllKeys();
        const now = Date.now();

        for (const key of allKeys) {
            try {
                const data = JSON.parse(this.storage.getItem(key));
                if (data.timestamp && (now - data.timestamp) > this.options.cacheTTL) {
                    this.storage.removeItem(key);
                }
            } catch {
                // Invalid data, remove it
                this.storage.removeItem(key);
            }
        }
    }
}

// Make available globally
window.ModuleRegistry = ModuleRegistry;

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ModuleRegistry;
}

console.log('ModuleRegistry', '[ModuleRegistry] Class loaded successfully');