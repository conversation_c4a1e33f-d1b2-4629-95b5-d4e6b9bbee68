/**
 * FileSystemHandlers.js
 * File system operation IPC handlers for main process
 */

const { ipcMain, app } = require('electron');
const fs = require('fs').promises;
const path = require('path');
const os = require('os');

class FileSystemHandlers {
    constructor(logger) {
        this.logger = logger;
        this.coreDeskPath = this.getDefaultCoreDeskPath();

        // Log the CoreDesk path for debugging
        this.logger?.info('FileSystemHandlers', 'CoreDesk path initialized', {
            path: this.coreDeskPath,
            platform: process.platform,
            userHome: os.homedir(),
            documentsPath: process.platform === 'win32' ? app.getPath('documents') : 'N/A'
        });

        this.allowedPaths = [
            app.getPath('userData'),
            app.getPath('temp'),
            app.getPath('downloads'),
            app.getPath('documents'),
            this.coreDeskPath,
            os.homedir(), // Allow access to user home directory
            path.dirname(os.homedir()) // Allow access to parent of user directory (for navigation)
        ];
        this.setupHandlers();
        this.initializeCoreDeskDirectory();
    }

    setupHandlers() {
        ipcMain.handle('fs:writeFile', async (event, filePath, data, options = {}) => {
            return this.writeFile(filePath, data, options);
        });

        ipcMain.handle('fs:readFile', async (event, filePath, options = {}) => {
            return this.readFile(filePath, options);
        });

        ipcMain.handle('fs:deleteFile', async (event, filePath) => {
            return this.deleteFile(filePath);
        });

        ipcMain.handle('fs:exists', async (event, filePath) => {
            return this.fileExists(filePath);
        });

        ipcMain.handle('fs:createDirectory', async (event, dirPath) => {
            return this.createDirectory(dirPath);
        });

        ipcMain.handle('fs:listDirectory', async (event, dirPath) => {
            return this.listDirectory(dirPath);
        });

        // Enhanced file system APIs for file explorer
        ipcMain.handle('fs:readDirectory', async (event, dirPath) => {
            return this.readDirectory(dirPath);
        });

        ipcMain.handle('fs:getFileStats', async (event, filePath) => {
            return this.getFileStats(filePath);
        });

        ipcMain.handle('fs:getHomeDirectory', async (event) => {
            return this.getHomeDirectory();
        });

        ipcMain.handle('fs:getCoreDeskPath', async (event) => {
            return this.getCoreDeskPath();
        });

        ipcMain.handle('fs:setCoreDeskPath', async (event, newPath) => {
            return this.setCoreDeskPath(newPath);
        });

        ipcMain.handle('fs:navigateToParent', async (event, currentPath) => {
            return this.navigateToParent(currentPath);
        });
    }

    async writeFile(filePath, data, options = {}) {
        try {
            // Security: Validate file path
            if (!this.isPathAllowed(filePath)) {
                throw new Error('Access denied: Path not allowed');
            }

            // Input validation
            if (!filePath || typeof filePath !== 'string') {
                throw new Error('Invalid file path');
            }

            if (data === null || data === undefined) {
                throw new Error('Invalid data');
            }

            // Ensure directory exists
            const dir = path.dirname(filePath);
            await fs.mkdir(dir, { recursive: true });

            // Write file
            await fs.writeFile(filePath, data, options);

            this.logger?.info('FileSystemHandlers', 'File written successfully', {
                path: filePath,
                size: data.length
            });

            return {
                success: true,
                path: filePath
            };

        } catch (error) {
            this.logger?.error('FileSystemHandlers', 'File write failed', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    async readFile(filePath, options = {}) {
        try {
            // Security: Validate file path
            if (!this.isPathAllowed(filePath)) {
                throw new Error('Access denied: Path not allowed');
            }

            // Input validation
            if (!filePath || typeof filePath !== 'string') {
                throw new Error('Invalid file path');
            }

            // Check if file exists
            await fs.access(filePath);

            // Read file
            const data = await fs.readFile(filePath, options);

            this.logger?.debug('FileSystemHandlers', 'File read successfully', {
                path: filePath,
                size: data.length
            });

            return {
                success: true,
                data: data,
                path: filePath
            };

        } catch (error) {
            this.logger?.error('FileSystemHandlers', 'File read failed', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    async deleteFile(filePath) {
        try {
            // Security: Validate file path
            if (!this.isPathAllowed(filePath)) {
                throw new Error('Access denied: Path not allowed');
            }

            // Input validation
            if (!filePath || typeof filePath !== 'string') {
                throw new Error('Invalid file path');
            }

            // Delete file
            await fs.unlink(filePath);

            this.logger?.info('FileSystemHandlers', 'File deleted successfully', {
                path: filePath
            });

            return {
                success: true,
                path: filePath
            };

        } catch (error) {
            this.logger?.error('FileSystemHandlers', 'File deletion failed', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    async fileExists(filePath) {
        try {
            // Security: Validate file path
            if (!this.isPathAllowed(filePath)) {
                return { success: false, exists: false, error: 'Path not allowed' };
            }

            // Input validation
            if (!filePath || typeof filePath !== 'string') {
                throw new Error('Invalid file path');
            }

            // Check existence
            await fs.access(filePath);

            return {
                success: true,
                exists: true,
                path: filePath
            };

        } catch (error) {
            return {
                success: true,
                exists: false,
                path: filePath
            };
        }
    }

    async createDirectory(dirPath) {
        try {
            // Security: Validate directory path
            if (!this.isPathAllowed(dirPath)) {
                throw new Error('Access denied: Path not allowed');
            }

            // Input validation
            if (!dirPath || typeof dirPath !== 'string') {
                throw new Error('Invalid directory path');
            }

            // Create directory
            await fs.mkdir(dirPath, { recursive: true });

            this.logger?.info('FileSystemHandlers', 'Directory created successfully', {
                path: dirPath
            });

            return {
                success: true,
                path: dirPath
            };

        } catch (error) {
            this.logger?.error('FileSystemHandlers', 'Directory creation failed', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    async listDirectory(dirPath) {
        try {
            // Security: Validate directory path
            if (!this.isPathAllowed(dirPath)) {
                throw new Error('Access denied: Path not allowed');
            }

            // Input validation
            if (!dirPath || typeof dirPath !== 'string') {
                throw new Error('Invalid directory path');
            }

            // List directory contents
            const items = await fs.readdir(dirPath, { withFileTypes: true });

            const files = [];
            const directories = [];

            for (const item of items) {
                const itemInfo = {
                    name: item.name,
                    path: path.join(dirPath, item.name),
                    isDirectory: item.isDirectory(),
                    isFile: item.isFile()
                };

                if (item.isDirectory()) {
                    directories.push(itemInfo);
                } else {
                    files.push(itemInfo);
                }
            }

            this.logger?.debug('FileSystemHandlers', 'Directory listed successfully', {
                path: dirPath,
                fileCount: files.length,
                dirCount: directories.length
            });

            return {
                success: true,
                path: dirPath,
                files: files,
                directories: directories,
                total: files.length + directories.length
            };

        } catch (error) {
            this.logger?.error('FileSystemHandlers', 'Directory listing failed', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Get default CoreDesk directory path
     */
    getDefaultCoreDeskPath() {
        // Use Documents folder on Windows for better user experience
        if (process.platform === 'win32') {
            const documentsPath = app.getPath('documents');
            return path.join(documentsPath, 'CoreDesk');
        } else {
            // Use home directory on other platforms
            const userHome = os.homedir();
            return path.join(userHome, 'coredesk');
        }
    }

    /**
     * Initialize CoreDesk directory structure
     */
    async initializeCoreDeskDirectory() {
        try {
            // Create main coredesk directory
            await fs.mkdir(this.coreDeskPath, { recursive: true });

            // Create subdirectories
            const subdirs = ['documents', 'projects', 'templates', 'exports', 'backups', 'modules'];
            for (const subdir of subdirs) {
                await fs.mkdir(path.join(this.coreDeskPath, subdir), { recursive: true });
            }

            // Create a welcome file if it doesn't exist
            const welcomeFile = path.join(this.coreDeskPath, 'Bienvenido.txt');
            const welcomeExists = await this.fileExists(welcomeFile);
            if (!welcomeExists.exists) {
                const welcomeContent = `Bienvenido a CoreDesk Framework

Este es tu directorio de trabajo personal de CoreDesk.

Estructura de directorios:
- documents/  : Documentos y archivos de trabajo
- projects/   : Proyectos activos
- templates/  : Plantillas y formularios
- exports/    : Archivos exportados
- backups/    : Copias de seguridad

Puedes personalizar esta estructura según tus necesidades.
La ruta de este directorio se puede cambiar en la configuración.

CoreDesk Framework v2.0.0
`;
                await fs.writeFile(welcomeFile, welcomeContent, 'utf8');
            }

            this.logger?.info('FileSystemHandlers', 'CoreDesk directory initialized', {
                path: this.coreDeskPath
            });

        } catch (error) {
            this.logger?.error('FileSystemHandlers', 'Failed to initialize CoreDesk directory', error);
        }
    }

    /**
     * Enhanced directory reading with file statistics
     */
    async readDirectory(dirPath) {
        try {
            // Security: Validate directory path
            if (!this.isPathAllowed(dirPath)) {
                throw new Error('Access denied: Path not allowed');
            }

            // Input validation
            if (!dirPath || typeof dirPath !== 'string') {
                throw new Error('Invalid directory path');
            }

            // Read directory contents with file types
            const items = await fs.readdir(dirPath, { withFileTypes: true });
            const results = [];

            for (const item of items) {
                const itemPath = path.join(dirPath, item.name);
                
                try {
                    const stats = await fs.stat(itemPath);
                    const itemInfo = {
                        name: item.name,
                        path: itemPath,
                        type: item.isDirectory() ? 'directory' : 'file',
                        isDirectory: item.isDirectory(),
                        isFile: item.isFile(),
                        size: item.isFile() ? stats.size : null,
                        modified: stats.mtime.toISOString(),
                        created: stats.birthtime.toISOString(),
                        extension: item.isFile() ? path.extname(item.name).toLowerCase().substring(1) : null
                    };
                    results.push(itemInfo);
                } catch (statError) {
                    // Skip items that can't be accessed
                    this.logger?.warn('FileSystemHandlers', 'Could not stat item', {
                        path: itemPath,
                        error: statError.message
                    });
                }
            }

            // Sort: directories first, then files, both alphabetically
            results.sort((a, b) => {
                if (a.isDirectory && !b.isDirectory) return -1;
                if (!a.isDirectory && b.isDirectory) return 1;
                return a.name.toLowerCase().localeCompare(b.name.toLowerCase());
            });

            this.logger?.debug('FileSystemHandlers', 'Directory read successfully', {
                path: dirPath,
                itemCount: results.length
            });

            return {
                success: true,
                path: dirPath,
                items: results
            };

        } catch (error) {
            this.logger?.error('FileSystemHandlers', 'Directory read failed', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Get file/directory statistics
     */
    async getFileStats(filePath) {
        try {
            // Security: Validate file path
            if (!this.isPathAllowed(filePath)) {
                throw new Error('Access denied: Path not allowed');
            }

            const stats = await fs.stat(filePath);
            
            return {
                success: true,
                stats: {
                    size: stats.size,
                    created: stats.birthtime.toISOString(),
                    modified: stats.mtime.toISOString(),
                    accessed: stats.atime.toISOString(),
                    isDirectory: stats.isDirectory(),
                    isFile: stats.isFile(),
                    permissions: stats.mode
                }
            };

        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Get user home directory
     */
    async getHomeDirectory() {
        try {
            const homeDir = os.homedir();
            return {
                success: true,
                path: homeDir
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Get current CoreDesk path
     */
    async getCoreDeskPath() {
        return {
            success: true,
            path: this.coreDeskPath
        };
    }

    /**
     * Set new CoreDesk path
     */
    async setCoreDeskPath(newPath) {
        try {
            if (!newPath || typeof newPath !== 'string') {
                throw new Error('Invalid path');
            }

            // Validate and create the new directory
            await fs.mkdir(newPath, { recursive: true });
            
            // Update the path and allowed paths
            const oldPath = this.coreDeskPath;
            this.coreDeskPath = path.resolve(newPath);
            
            // Update allowed paths
            const oldIndex = this.allowedPaths.indexOf(oldPath);
            if (oldIndex !== -1) {
                this.allowedPaths[oldIndex] = this.coreDeskPath;
            } else {
                this.allowedPaths.push(this.coreDeskPath);
            }

            // Initialize the new directory
            await this.initializeCoreDeskDirectory();

            this.logger?.info('FileSystemHandlers', 'CoreDesk path updated', {
                oldPath,
                newPath: this.coreDeskPath
            });

            return {
                success: true,
                oldPath,
                newPath: this.coreDeskPath
            };

        } catch (error) {
            this.logger?.error('FileSystemHandlers', 'Failed to set CoreDesk path', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Navigate to parent directory
     */
    async navigateToParent(currentPath) {
        try {
            if (!currentPath || typeof currentPath !== 'string') {
                throw new Error('Invalid current path');
            }

            const parentPath = path.dirname(currentPath);
            
            // Don't go above certain system boundaries
            if (parentPath === currentPath) {
                // We're at the root
                return {
                    success: false,
                    error: 'Already at root directory'
                };
            }

            return {
                success: true,
                path: parentPath
            };

        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    isPathAllowed(filePath) {
        if (!filePath || typeof filePath !== 'string') {
            return false;
        }

        // Resolve path to prevent directory traversal
        const resolvedPath = path.resolve(filePath);

        // Check if path is within allowed directories
        return this.allowedPaths.some(allowedPath => {
            const resolvedAllowedPath = path.resolve(allowedPath);
            return resolvedPath.startsWith(resolvedAllowedPath);
        });
    }

    cleanup() {
        ipcMain.removeAllListeners('fs:writeFile');
        ipcMain.removeAllListeners('fs:readFile');
        ipcMain.removeAllListeners('fs:deleteFile');
        ipcMain.removeAllListeners('fs:exists');
        ipcMain.removeAllListeners('fs:createDirectory');
        ipcMain.removeAllListeners('fs:listDirectory');
        ipcMain.removeAllListeners('fs:readDirectory');
        ipcMain.removeAllListeners('fs:getFileStats');
        ipcMain.removeAllListeners('fs:getHomeDirectory');
        ipcMain.removeAllListeners('fs:getCoreDeskPath');
        ipcMain.removeAllListeners('fs:setCoreDeskPath');
        ipcMain.removeAllListeners('fs:navigateToParent');
    }
}

module.exports = FileSystemHandlers;