/**
 * Script de prueba para verificar la ubicación correcta de módulos
 * Este script simula la instalación de un módulo para verificar que se cree en la ubicación segura
 */

const { app, BrowserWindow } = require('electron');
const path = require('path');
const fs = require('fs').promises;
const os = require('os');

// Simular el comportamiento del FileSystemHandlers
class TestFileSystemHandlers {
    constructor() {
        this.coreDeskPath = this.getDefaultCoreDeskPath();
        this.modulesPath = this.getModulesDirectoryPath();
        
        console.log('🔍 Test Paths Initialized:');
        console.log('  Platform:', process.platform);
        console.log('  CoreDesk Path:', this.coreDeskPath);
        console.log('  Modules Path:', this.modulesPath);
        console.log('  App Path:', app.getAppPath());
        console.log('  Executable Path:', process.execPath);
        console.log('  Is Packaged:', app.isPackaged);
        console.log('  User Data:', app.getPath('userData'));
        console.log('  Documents:', app.getPath('documents'));
    }

    getDefaultCoreDeskPath() {
        if (process.platform === 'win32') {
            const documentsPath = app.getPath('documents');
            return path.join(documentsPath, 'CoreDesk');
        } else {
            const userHome = os.homedir();
            return path.join(userHome, 'coredesk');
        }
    }

    getModulesDirectoryPath() {
        if (process.platform === 'win32') {
            try {
                const executablePath = process.execPath;
                const installDir = path.dirname(executablePath);
                
                console.log('🔧 Windows Path Calculation:');
                console.log('  Executable Path:', executablePath);
                console.log('  Install Directory:', installDir);
                
                return path.join(installDir, 'modulos');
            } catch (error) {
                console.error('❌ Error getting Windows modules path:', error);
                const appDataPath = app.getPath('userData');
                return path.join(appDataPath, 'modulos');
            }
        } else {
            return path.join(this.coreDeskPath, 'modulos');
        }
    }

    async testModulesDirectoryCreation() {
        console.log('\n🧪 Testing Modules Directory Creation...');
        
        try {
            // Test if we can create the modules directory
            await fs.mkdir(this.modulesPath, { recursive: true });
            console.log('✅ Modules directory created successfully:', this.modulesPath);
            
            // Test write permissions
            const testFile = path.join(this.modulesPath, '.write-test');
            await fs.writeFile(testFile, 'test content', 'utf8');
            console.log('✅ Write permissions confirmed');
            
            // Clean up test file
            await fs.unlink(testFile);
            console.log('✅ Test file cleaned up');
            
            // Test module installation simulation
            await this.simulateModuleInstallation();
            
        } catch (error) {
            console.error('❌ Error testing modules directory:', error);
            
            if (process.platform === 'win32') {
                console.log('🔄 Trying fallback location...');
                const fallbackPath = path.join(app.getPath('userData'), 'modulos');
                try {
                    await fs.mkdir(fallbackPath, { recursive: true });
                    this.modulesPath = fallbackPath;
                    console.log('✅ Fallback modules directory created:', this.modulesPath);
                } catch (fallbackError) {
                    console.error('❌ Fallback also failed:', fallbackError);
                }
            }
        }
    }

    async simulateModuleInstallation() {
        console.log('\n📦 Simulating Module Installation...');
        
        const testModuleId = 'test-module';
        const modulePath = path.join(this.modulesPath, testModuleId);
        
        try {
            // Create module directory
            await fs.mkdir(modulePath, { recursive: true });
            console.log('✅ Module directory created:', modulePath);
            
            // Create module files
            const moduleFiles = {
                [`${testModuleId}.js`]: 'console.log("Test module loaded");',
                [`${testModuleId}.css`]: '.test-module { color: blue; }',
                'manifest.json': JSON.stringify({
                    id: testModuleId,
                    name: 'Test Module',
                    version: '1.0.0',
                    description: 'Test module for path verification'
                }, null, 2)
            };
            
            for (const [filename, content] of Object.entries(moduleFiles)) {
                const filePath = path.join(modulePath, filename);
                await fs.writeFile(filePath, content, 'utf8');
                console.log('✅ Created file:', filename);
            }
            
            console.log('🎉 Module installation simulation completed successfully!');
            
            // List directory contents
            const files = await fs.readdir(modulePath);
            console.log('📁 Module directory contents:', files);
            
        } catch (error) {
            console.error('❌ Module installation simulation failed:', error);
        }
    }

    async cleanup() {
        console.log('\n🧹 Cleaning up test files...');
        try {
            const testModulePath = path.join(this.modulesPath, 'test-module');
            await fs.rm(testModulePath, { recursive: true, force: true });
            console.log('✅ Test module cleaned up');
        } catch (error) {
            console.log('⚠️ Cleanup warning:', error.message);
        }
    }
}

// Main test function
async function runTest() {
    console.log('🚀 CoreDesk Modules Path Test');
    console.log('================================\n');
    
    const tester = new TestFileSystemHandlers();
    
    try {
        await tester.testModulesDirectoryCreation();
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
        await tester.cleanup();
        
        console.log('\n✅ All tests completed successfully!');
        console.log('\n📋 Summary:');
        console.log('  Platform:', process.platform);
        console.log('  Final Modules Path:', tester.modulesPath);
        console.log('  Security Level:', process.platform === 'win32' ? 'HIGH (Install Directory)' : 'MEDIUM (User Directory)');
        
    } catch (error) {
        console.error('\n❌ Test failed:', error);
    }
    
    // Keep window open for a few seconds to see results
    setTimeout(() => {
        app.quit();
    }, 5000);
}

// Electron app setup
app.whenReady().then(() => {
    // Create a hidden window (required for Electron to work)
    const win = new BrowserWindow({
        width: 800,
        height: 600,
        show: false,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false
        }
    });
    
    // Run the test
    runTest();
});

app.on('window-all-closed', () => {
    app.quit();
});
